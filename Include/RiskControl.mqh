//+------------------------------------------------------------------+
//|                                                 RiskControl.mqh |
//|                                  Copyright 2024, Fibonacci EA    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Fibonacci EA"
#property link      "https://www.mql5.com"
#property version   "1.00"

//+------------------------------------------------------------------+
//| Risk Statistics Structure                                       |
//+------------------------------------------------------------------+
struct RiskStats
{
   double daily_pnl;
   double max_drawdown;
   int    open_trades;
   double used_margin;
   double free_margin;
   double equity;
   double balance;
   datetime last_reset;
};

//+------------------------------------------------------------------+
//| Risk Control Class                                             |
//+------------------------------------------------------------------+
class CRiskControl
{
private:
   string            m_symbol;
   
   // Risk parameters
   double            m_risk_per_trade_pct;
   int               m_max_trades;
   double            m_daily_loss_limit_pct;
   double            m_max_drawdown_pct;
   double            m_min_free_margin_pct;
   
   // Statistics
   RiskStats         m_stats;
   double            m_start_balance;
   double            m_daily_start_balance;
   
   // State
   bool              m_trading_allowed;
   bool              m_initialized;
   string            m_last_error;

public:
   // Constructor
   CRiskControl(void);
   ~CRiskControl(void);
   
   // Initialization
   bool              Init(string symbol);
   void              Deinit(void);
   
   // Configuration
   void              SetRiskPerTrade(double pct) { m_risk_per_trade_pct = pct; }
   void              SetMaxTrades(int max_trades) { m_max_trades = max_trades; }
   void              SetDailyLossLimit(double pct) { m_daily_loss_limit_pct = pct; }
   void              SetMaxDrawdown(double pct) { m_max_drawdown_pct = pct; }
   void              SetMinFreeMargin(double pct) { m_min_free_margin_pct = pct; }
   
   // Main methods
   bool              Update(void);
   bool              IsTradingAllowed(void);
   bool              CanOpenNewTrade(void);
   double            CalculateLotSize(double entry_price, double stop_price);
   bool              ValidateOrder(ENUM_ORDER_TYPE order_type, double lot_size, double entry_price, double stop_price);
   
   // Getters
   RiskStats         GetStats(void) { return m_stats; }
   string            GetLastError(void) { return m_last_error; }
   double            GetRiskPerTrade(void) { return m_risk_per_trade_pct; }
   int               GetMaxTrades(void) { return m_max_trades; }
   bool              IsTradingEnabled(void) { return m_trading_allowed; }
   
   // Manual controls
   void              EnableTrading(void) { m_trading_allowed = true; }
   void              DisableTrading(void) { m_trading_allowed = false; }
   void              ResetDailyStats(void);

private:
   // Internal methods
   void              UpdateStats(void);
   bool              CheckDailyLossLimit(void);
   bool              CheckMaxDrawdown(void);
   bool              CheckMarginRequirements(void);
   bool              CheckMaxTrades(void);
   double            GetAccountEquity(void);
   double            GetAccountBalance(void);
   double            GetAccountFreeMargin(void);
   double            GetAccountMargin(void);
   int               CountOpenTrades(void);
   double            CalculateDailyPnL(void);
   void              SetError(string error);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CRiskControl::CRiskControl(void)
{
   m_symbol = "";
   m_risk_per_trade_pct = 1.0;
   m_max_trades = 3;
   m_daily_loss_limit_pct = 5.0;
   m_max_drawdown_pct = 15.0;
   m_min_free_margin_pct = 20.0;
   
   m_start_balance = 0;
   m_daily_start_balance = 0;
   m_trading_allowed = true;
   m_initialized = false;
   m_last_error = "";
   
   ZeroMemory(m_stats);
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CRiskControl::~CRiskControl(void)
{
   Deinit();
}

//+------------------------------------------------------------------+
//| Initialize risk control                                         |
//+------------------------------------------------------------------+
bool CRiskControl::Init(string symbol)
{
   m_symbol = symbol;
   m_start_balance = GetAccountBalance();
   m_daily_start_balance = m_start_balance;
   
   // Initialize stats
   m_stats.last_reset = TimeCurrent();
   UpdateStats();
   
   m_initialized = true;
   Print("RiskControl initialized for ", m_symbol);
   return true;
}

//+------------------------------------------------------------------+
//| Cleanup                                                         |
//+------------------------------------------------------------------+
void CRiskControl::Deinit(void)
{
   m_initialized = false;
}

//+------------------------------------------------------------------+
//| Update risk statistics and check limits                        |
//+------------------------------------------------------------------+
bool CRiskControl::Update(void)
{
   if(!m_initialized)
      return false;
      
   // Check if new day started
   datetime current_time = TimeCurrent();
   MqlDateTime dt_current, dt_last;
   TimeToStruct(current_time, dt_current);
   TimeToStruct(m_stats.last_reset, dt_last);
   
   if(dt_current.day != dt_last.day)
   {
      ResetDailyStats();
   }
   
   // Update current statistics
   UpdateStats();
   
   // Check all risk limits
   bool trading_ok = true;
   
   if(!CheckDailyLossLimit())
   {
      SetError("Daily loss limit exceeded");
      trading_ok = false;
   }
   
   if(!CheckMaxDrawdown())
   {
      SetError("Maximum drawdown exceeded");
      trading_ok = false;
   }
   
   if(!CheckMarginRequirements())
   {
      SetError("Insufficient margin");
      trading_ok = false;
   }
   
   m_trading_allowed = trading_ok;
   return true;
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                    |
//+------------------------------------------------------------------+
bool CRiskControl::IsTradingAllowed(void)
{
   return m_trading_allowed && m_initialized;
}

//+------------------------------------------------------------------+
//| Check if new trade can be opened                               |
//+------------------------------------------------------------------+
bool CRiskControl::CanOpenNewTrade(void)
{
   if(!IsTradingAllowed())
      return false;
      
   if(!CheckMaxTrades())
   {
      SetError("Maximum number of trades reached");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                    |
//+------------------------------------------------------------------+
double CRiskControl::CalculateLotSize(double entry_price, double stop_price)
{
   if(entry_price <= 0 || stop_price <= 0 || entry_price == stop_price)
   {
      SetError("Invalid entry or stop price");
      return 0;
   }
   
   double equity = GetAccountEquity();
   double risk_amount = equity * (m_risk_per_trade_pct / 100.0);
   
   double price_diff = MathAbs(entry_price - stop_price);
   double tick_value = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_SIZE);
   double lot_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   double min_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   
   if(tick_value == 0 || tick_size == 0)
   {
      SetError("Invalid symbol tick data");
      return 0;
   }
   
   // Calculate lot size
   double ticks = price_diff / tick_size;
   double risk_per_lot = ticks * tick_value;
   double lot_size = risk_amount / risk_per_lot;
   
   // Normalize lot size to avoid floating-point precision issues
   int step_digits = (int)MathRound(-MathLog10(lot_step));
   lot_size = NormalizeDouble(MathFloor(lot_size / lot_step) * lot_step, step_digits);
   
   // Apply limits
   if(lot_size < min_lot) lot_size = min_lot;
   if(lot_size > max_lot) lot_size = max_lot;
   
   return lot_size;
}

//+------------------------------------------------------------------+
//| Validate order parameters                                      |
//+------------------------------------------------------------------+
bool CRiskControl::ValidateOrder(ENUM_ORDER_TYPE order_type, double lot_size, double entry_price, double stop_price)
{
   // Check lot size
   double min_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   
   if(lot_size < min_lot || lot_size > max_lot)
   {
      SetError("Invalid lot size");
      return false;
   }
   
   // Use tolerance-based checking to handle floating-point precision issues
   double eps = lot_step * 0.0001; // 0.01% tolerance
   if(MathAbs(MathMod(lot_size, lot_step)) > eps)
   {
      SetError("Lot size not aligned to step");
      return false;
   }
   
   // Check prices
   if(entry_price <= 0 || stop_price <= 0)
   {
      SetError("Invalid prices");
      return false;
   }
   
   // Check margin requirements
   double margin_required = 0;
   if(!OrderCalcMargin(order_type, m_symbol, lot_size, entry_price, margin_required))
   {
      SetError("Cannot calculate margin");
      return false;
   }
   
   if(margin_required > GetAccountFreeMargin() * 0.8) // Use max 80% of free margin
   {
      SetError("Insufficient margin for order");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Reset daily statistics                                         |
//+------------------------------------------------------------------+
void CRiskControl::ResetDailyStats(void)
{
   m_daily_start_balance = GetAccountBalance();
   m_stats.last_reset = TimeCurrent();
   m_stats.daily_pnl = 0;
   
   Print("Daily risk statistics reset");
}

//+------------------------------------------------------------------+
//| Update current statistics                                      |
//+------------------------------------------------------------------+
void CRiskControl::UpdateStats(void)
{
   m_stats.equity = GetAccountEquity();
   m_stats.balance = GetAccountBalance();
   m_stats.free_margin = GetAccountFreeMargin();
   m_stats.used_margin = GetAccountMargin();
   m_stats.open_trades = CountOpenTrades();
   m_stats.daily_pnl = CalculateDailyPnL();
   m_stats.max_drawdown = ((m_start_balance - m_stats.equity) / m_start_balance) * 100.0;
}

//+------------------------------------------------------------------+
//| Check daily loss limit                                         |
//+------------------------------------------------------------------+
bool CRiskControl::CheckDailyLossLimit(void)
{
   double daily_loss_pct = (m_stats.daily_pnl / m_daily_start_balance) * 100.0;
   return (daily_loss_pct > -m_daily_loss_limit_pct);
}

//+------------------------------------------------------------------+
//| Check maximum drawdown                                         |
//+------------------------------------------------------------------+
bool CRiskControl::CheckMaxDrawdown(void)
{
   return (m_stats.max_drawdown < m_max_drawdown_pct);
}

//+------------------------------------------------------------------+
//| Check margin requirements                                      |
//+------------------------------------------------------------------+
bool CRiskControl::CheckMarginRequirements(void)
{
   double margin_level = (m_stats.equity / m_stats.used_margin) * 100.0;
   return (margin_level > m_min_free_margin_pct || m_stats.used_margin == 0);
}

//+------------------------------------------------------------------+
//| Check maximum trades limit                                     |
//+------------------------------------------------------------------+
bool CRiskControl::CheckMaxTrades(void)
{
   return (m_stats.open_trades < m_max_trades);
}

//+------------------------------------------------------------------+
//| Get account equity                                             |
//+------------------------------------------------------------------+
double CRiskControl::GetAccountEquity(void)
{
   return AccountInfoDouble(ACCOUNT_EQUITY);
}

//+------------------------------------------------------------------+
//| Get account balance                                            |
//+------------------------------------------------------------------+
double CRiskControl::GetAccountBalance(void)
{
   return AccountInfoDouble(ACCOUNT_BALANCE);
}

//+------------------------------------------------------------------+
//| Get account free margin                                        |
//+------------------------------------------------------------------+
double CRiskControl::GetAccountFreeMargin(void)
{
   return AccountInfoDouble(ACCOUNT_MARGIN_FREE);
}

//+------------------------------------------------------------------+
//| Get account used margin                                        |
//+------------------------------------------------------------------+
double CRiskControl::GetAccountMargin(void)
{
   return AccountInfoDouble(ACCOUNT_MARGIN);
}

//+------------------------------------------------------------------+
//| Count open trades for current symbol                          |
//+------------------------------------------------------------------+
int CRiskControl::CountOpenTrades(void)
{
   int count = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == m_symbol)
         count++;
   }
   
   return count;
}

//+------------------------------------------------------------------+
//| Calculate daily P&L                                           |
//+------------------------------------------------------------------+
double CRiskControl::CalculateDailyPnL(void)
{
   return m_stats.balance - m_daily_start_balance;
}

//+------------------------------------------------------------------+
//| Set error message                                              |
//+------------------------------------------------------------------+
void CRiskControl::SetError(string error)
{
   m_last_error = error;
   Print("RiskControl Error: ", error);
}
